"use client"
import { useEffect, useState } from "react"
import { useRole } from "@/components/dashboard/role-context"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"

interface Nominee {
  id: string
  name: string
  email: string
  relationship: string
  phone: string
  profile_photo_url?: string | null
  status: string
}

export default function TrusteeRequests() {
  const { currentRole } = useRole()
  const [loading, setLoading] = useState(false)
  const [nominees, setNominees] = useState<Nominee[]>([])
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (currentRole?.name !== "trustee" || !currentRole.relatedUser?.email) return
    setLoading(true)
    setError(null)
    fetch(`/api/trustee-nominee-requests?userEmail=${encodeURIComponent(currentRole.relatedUser.email)}`)
      .then(res => res.json())
      .then(data => {
        setNominees(data.nominees || [])
      })
      .catch(() => setError("Failed to load nominee requests"))
      .finally(() => setLoading(false))
  }, [currentRole])

  if (currentRole?.name !== "trustee") return null
  if (loading) return <div>Loading...</div>
  if (error) return <div>{error}</div>

  const allRequestsRaised = nominees.length > 0 && nominees.every(n => n.status === "pending" || n.status === "accepted")

  return (
    <div className="p-6 border rounded-lg bg-white mt-6">
      <div className="mb-4">
        <h2 className="text-xl font-bold flex items-center gap-2">
          <span>Group Request Approval</span>
        </h2>
        <div className="bg-blue-50 border border-blue-200 rounded p-3 mt-2 text-sm text-gray-700">
          This Section outlines the group request approval process: Access to specific user information will only be granted if all individual requests from the Nominees are submitted. Once all the requests are received, the Trustee is required to approve/reject the group request for access to user information
        </div>
      </div>
      <div className="mb-4">
        <h3 className="text-lg font-semibold">Dear {currentRole.relatedUser?.name || "Trustee"}</h3>
        <p className="text-gray-700 text-sm">You got below requests from the nominees of the user for whom you were appointed as trustee. Please go through their details clearly and then take the desired action needed. You can refer to the guidelines related to "Group Request Approval" if needed.</p>
      </div>
      <div className="flex flex-wrap gap-4 mb-4">
        {nominees.map(nominee => (
          <div key={nominee.id} className={`rounded-lg p-4 flex flex-col items-center w-48 shadow ${nominee.status === "pending" ? "bg-green-50 border-green-200 border" : "bg-red-50 border-red-200 border"}`}>
            <div className="w-20 h-20 rounded-full overflow-hidden bg-gray-200 mb-2">
              <Image src={nominee.profile_photo_url || "/placeholder.jpg"} alt={nominee.name} width={80} height={80} className="object-cover w-full h-full" />
            </div>
            <div className="font-bold text-center">{nominee.name}</div>
            <div className={`text-xs font-semibold mt-1 ${nominee.status === "pending" ? "text-green-700" : "text-red-700"}`}>{nominee.status === "pending" ? "Request Raised" : "Not Yet Raised"}</div>
            <div className="text-xs text-blue-700 underline cursor-pointer mt-2">View Nominee Details</div>
          </div>
        ))}
      </div>
      <div className="mb-4 text-yellow-700 bg-yellow-100 p-2 rounded text-xs">
        <b>Note:</b> The "Approve" and "Reject" Buttons will be enabled only if all the nominee requests are received. You can approve/reject after that.
      </div>
      <div className="flex gap-4">
        <Button disabled={!allRequestsRaised} className="bg-red-600 hover:bg-red-700">Reject</Button>
        <Button disabled={!allRequestsRaised} className="bg-green-600 hover:bg-green-700">Approve</Button>
      </div>
    </div>
  )
} 