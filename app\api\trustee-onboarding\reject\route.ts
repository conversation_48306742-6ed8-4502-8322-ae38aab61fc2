import { NextRequest, NextResponse } from "next/server"
import { rejectTrusteeInvitation } from "@/app/actions/trustees"

export async function POST(request: NextRequest) {
  try {
    const { trusteeId } = await request.json()

    if (!trusteeId) {
      return NextResponse.json(
        { success: false, error: "Trustee ID is required" },
        { status: 400 }
      )
    }

    const result = await rejectTrusteeInvitation(trusteeId)
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data
    })
  } catch (error) {
    console.error("Error in trustee reject API:", error)
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    )
  }
}
