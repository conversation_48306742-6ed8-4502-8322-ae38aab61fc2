import { NextRequest, NextResponse } from "next/server"
import { rejectTrusteeInvitation } from "@/app/actions/trustees"

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json()

    if (!token) {
      return NextResponse.json(
        { success: false, error: "Invitation token is required" },
        { status: 400 }
      )
    }

    const result = await rejectTrusteeInvitation(token)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      message: "Trustee invitation rejected successfully"
    })
  } catch (error) {
    console.error("Error in trustee reject API:", error)
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    )
  }
}
