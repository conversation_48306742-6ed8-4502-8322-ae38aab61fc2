import { NextRequest, NextResponse } from "next/server"
import { acceptTrusteeInvitation } from "@/app/actions/trustees"

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json()

    if (!token) {
      return NextResponse.json(
        { success: false, error: "Invitation token is required" },
        { status: 400 }
      )
    }

    const result = await acceptTrusteeInvitation(token)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      message: "Trustee invitation accepted successfully"
    })
  } catch (error) {
    console.error("Error in trustee accept API:", error)
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    )
  }
}
