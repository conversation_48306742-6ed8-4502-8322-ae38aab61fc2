# Trustee Invitation System Implementation

## Overview
This document describes the complete implementation of the trustee invitation system, including invitation creation, acceptance/rejection flow, role management, and notifications.

## System Flow

### 1. Trustee Invitation Creation
When a user invites someone to be their trustee:

1. **Generate Invitation Token**: Unique UUID token for secure invitation
2. **Create Trustee Record**: Insert into `trustees` table with `status: 'pending'`
3. **Send Notification**: Notify the invited user (if they have an account)
4. **Email Invitation**: Send invitation email with secure token link

### 2. Invitation Response Flow
When the invited person responds to the invitation:

#### **Acceptance Flow**:
1. **Validate Token**: Verify invitation token and user email match
2. **Update Status**: Change `status` to 'accepted' in trustees table
3. **Assign Role**: Add 'trustee' role to `user_roles` table with relationship
4. **Send Notification**: Notify the inviter about acceptance
5. **Grant Access**: User gains trustee permissions

#### **Rejection Flow**:
1. **Validate Token**: Verify invitation token and user email match
2. **Update Status**: Change `status` to 'rejected' in trustees table
3. **Send Notification**: Notify the inviter about rejection
4. **No Role Assignment**: User does not gain trustee permissions

## Database Schema Updates

### Trustees Table
```sql
CREATE TABLE public.trustees (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    relationship TEXT,
    phone TEXT,
    profile_photo_url TEXT,
    government_id_url TEXT,
    approval_type TEXT NOT NULL,
    status TEXT DEFAULT 'pending',          -- NEW: pending, accepted, rejected
    invitation_token TEXT UNIQUE,           -- NEW: Unique invitation token
    invitation_sent_at TIMESTAMPTZ,         -- NEW: When invitation was sent
    invitation_responded_at TIMESTAMPTZ,    -- NEW: When user responded
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### User Roles Table (Enhanced)
```sql
CREATE TABLE public.user_roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES public.roles(id) ON DELETE CASCADE,
    related_user_id UUID REFERENCES public.users(id), -- The inviter's ID
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, role_id, related_user_id)
);
```

## API Endpoints

### 1. Accept Invitation
**Endpoint**: `POST /api/trustee-onboarding/accept`
**Body**: `{ "token": "invitation-token" }`
**Response**: 
```json
{
  "success": true,
  "data": { /* trustee data */ },
  "message": "Trustee invitation accepted successfully"
}
```

### 2. Reject Invitation
**Endpoint**: `POST /api/trustee-onboarding/reject`
**Body**: `{ "token": "invitation-token" }`
**Response**: 
```json
{
  "success": true,
  "data": { /* trustee data */ },
  "message": "Trustee invitation rejected successfully"
}
```

### 3. Get Invitation Details
**Function**: `getTrusteeInvitationByToken(token)`
**Returns**: Invitation details and inviter information

## Key Functions

### 1. addTrustee()
- Creates trustee record with invitation token
- Sends notification to invited user
- Generates secure invitation link

### 2. acceptTrusteeInvitation(token)
- Validates token and user authorization
- Updates trustee status to 'accepted'
- Assigns trustee role in user_roles table
- Sends acceptance notification to inviter

### 3. rejectTrusteeInvitation(token)
- Validates token and user authorization
- Updates trustee status to 'rejected'
- Sends rejection notification to inviter

### 4. getTrusteeInvitationByToken(token)
- Retrieves invitation details
- Validates invitation status
- Returns inviter information

## Security Features

### 1. Token-Based Authentication
- Unique UUID tokens for each invitation
- Tokens are single-use and expire when responded to
- Email verification ensures only intended recipient can respond

### 2. Authorization Checks
- User must be authenticated to respond to invitations
- User's email must match the invitation email
- Prevents unauthorized access to invitations

### 3. Status Validation
- Prevents multiple responses to same invitation
- Clear error messages for invalid states
- Proper cleanup of expired invitations

## Notification System

### 1. Invitation Sent
```json
{
  "user_id": "trustee-user-id",
  "title": "Trustee Invitation",
  "message": "John Doe has invited you to be their trustee.",
  "type": "invitation_received",
  "data": {
    "invitationLink": "/trustee-onboarding?token=...",
    "trusteeId": "trustee-record-id",
    "inviterName": "John Doe"
  }
}
```

### 2. Invitation Accepted
```json
{
  "user_id": "inviter-user-id",
  "title": "Trustee Invitation Accepted",
  "message": "Jane Smith (<EMAIL>) has accepted your trustee invitation.",
  "type": "invitation_received",
  "data": {
    "trusteeId": "trustee-record-id",
    "trusteeName": "Jane Smith",
    "trusteeEmail": "<EMAIL>",
    "action": "accepted"
  }
}
```

### 3. Invitation Rejected
```json
{
  "user_id": "inviter-user-id",
  "title": "Trustee Invitation Rejected",
  "message": "Jane Smith (<EMAIL>) has rejected your trustee invitation.",
  "type": "invitation_received",
  "data": {
    "trusteeId": "trustee-record-id",
    "trusteeName": "Jane Smith",
    "trusteeEmail": "<EMAIL>",
    "action": "rejected"
  }
}
```

## Role Management

### 1. Role Assignment on Acceptance
When a trustee accepts an invitation:
- 'trustee' role is added to user_roles table
- `related_user_id` points to the inviter
- User gains access to trustee-specific sections
- Permissions are enforced through RLS policies

### 2. Role Removal on Rejection
When a trustee rejects an invitation:
- No role is assigned
- User maintains their existing roles
- No access to inviter's data

## Error Handling

### Common Error Scenarios
1. **Invalid Token**: Token doesn't exist or is malformed
2. **Unauthorized User**: User email doesn't match invitation
3. **Already Responded**: Invitation already accepted/rejected
4. **User Not Found**: Invited user doesn't have an account
5. **Role Assignment Failed**: Technical error in role assignment

### Error Response Format
```json
{
  "success": false,
  "error": "Descriptive error message",
  "status": "current-invitation-status" // if applicable
}
```

## Testing Scenarios

### 1. Happy Path - Acceptance
1. User A invites User B as trustee
2. User B receives notification and email
3. User B clicks invitation link
4. User B accepts invitation
5. User A receives acceptance notification
6. User B gains trustee role and permissions

### 2. Happy Path - Rejection
1. User A invites User B as trustee
2. User B receives notification and email
3. User B clicks invitation link
4. User B rejects invitation
5. User A receives rejection notification
6. User B does not gain any new permissions

### 3. Edge Cases
1. Invalid/expired tokens
2. Multiple response attempts
3. Unauthorized access attempts
4. Network failures during role assignment

## Maintenance and Monitoring

### 1. Regular Cleanup
- Monitor for expired pending invitations
- Clean up unused invitation tokens
- Archive old notification records

### 2. Performance Monitoring
- Track invitation response rates
- Monitor role assignment success rates
- Alert on failed notifications

### 3. Security Auditing
- Log all invitation responses
- Monitor for suspicious token usage
- Regular review of role assignments

This implementation provides a secure, robust, and user-friendly trustee invitation system with proper error handling, notifications, and role management.
