/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    serverActions: {
      bodySizeLimit: '10mb',
    },
    // Enable partial prerendering for better performance
    ppr: true,
  },
  images: {
    domains: ['fxtxegzuubiunsanfzbb.supabase.co'],
    // Optimize image loading
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
  },
  // Enable compression
  compress: true,
  // Optimize bundle
  swcMinify: true,
  // Enable static optimization
  output: 'standalone',
  // Optimize fonts
  optimizeFonts: true,
  // Enable modern JavaScript features
  modularizeImports: {
    'lucide-react': {
      transform: 'lucide-react/dist/esm/icons/{{member}}',
    },
  },
}

module.exports = nextConfig