-- Migration: Add invitation fields to trustees table
-- Date: 2024-01-XX
-- Description: Adds status, invitation_token, invitation_sent_at, and invitation_responded_at fields to trustees table

-- Add new columns to trustees table
ALTER TABLE public.trustees 
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS invitation_token TEXT,
ADD COLUMN IF NOT EXISTS invitation_sent_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS invitation_responded_at TIMESTAMPTZ;

-- Add unique constraint on invitation_token
ALTER TABLE public.trustees 
ADD CONSTRAINT trustees_invitation_token_unique UNIQUE (invitation_token);

-- Add check constraint for status values
ALTER TABLE public.trustees 
ADD CONSTRAINT trustees_status_check CHECK (status IN ('pending', 'accepted', 'rejected'));

-- Create index for invitation_token lookups
CREATE INDEX IF NOT EXISTS idx_trustees_invitation_token ON public.trustees(invitation_token);

-- Create index for status filtering
CREATE INDEX IF NOT EXISTS idx_trustees_status ON public.trustees(status);

-- Update existing records to have 'accepted' status (assuming they were already accepted)
UPDATE public.trustees 
SET status = 'accepted', 
    invitation_responded_at = updated_at 
WHERE status IS NULL OR status = 'pending';

-- Create or update the trustee role if it doesn't exist
INSERT INTO public.roles (name, description) 
VALUES ('trustee', 'Trustee role for managing user accounts')
ON CONFLICT (name) DO UPDATE SET description = EXCLUDED.description;

-- Create RLS policies for trustees table if they don't exist
DO $$
BEGIN
    -- Policy for users to view their own trustee records
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'trustees' 
        AND policyname = 'Users can view own trustees'
    ) THEN
        CREATE POLICY "Users can view own trustees" ON public.trustees
        FOR SELECT USING (auth.uid() = user_id);
    END IF;

    -- Policy for users to manage their own trustee records
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'trustees' 
        AND policyname = 'Users can manage own trustees'
    ) THEN
        CREATE POLICY "Users can manage own trustees" ON public.trustees
        FOR ALL USING (auth.uid() = user_id);
    END IF;

    -- Policy for trustees to view their invitation details
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'trustees' 
        AND policyname = 'Users can view trustees where they are the trustee'
    ) THEN
        CREATE POLICY "Users can view trustees where they are the trustee" ON public.trustees
        FOR SELECT USING (
            EXISTS (SELECT 1 FROM public.users WHERE id = auth.uid() AND email = trustees.email)
        );
    END IF;
END
$$;

-- Enable RLS on trustees table if not already enabled
ALTER TABLE public.trustees ENABLE ROW LEVEL SECURITY;

-- Add comments to new columns
COMMENT ON COLUMN public.trustees.status IS 'Invitation status: pending, accepted, rejected';
COMMENT ON COLUMN public.trustees.invitation_token IS 'Unique token for invitation verification';
COMMENT ON COLUMN public.trustees.invitation_sent_at IS 'Timestamp when invitation was sent';
COMMENT ON COLUMN public.trustees.invitation_responded_at IS 'Timestamp when invitation was responded to';

-- Verify the migration
DO $$
BEGIN
    -- Check if all columns exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'trustees' AND column_name = 'status') THEN
        RAISE EXCEPTION 'Migration failed: status column not added';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'trustees' AND column_name = 'invitation_token') THEN
        RAISE EXCEPTION 'Migration failed: invitation_token column not added';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'trustees' AND column_name = 'invitation_sent_at') THEN
        RAISE EXCEPTION 'Migration failed: invitation_sent_at column not added';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'trustees' AND column_name = 'invitation_responded_at') THEN
        RAISE EXCEPTION 'Migration failed: invitation_responded_at column not added';
    END IF;
    
    RAISE NOTICE 'Migration completed successfully: All trustee invitation fields added';
END
$$;
